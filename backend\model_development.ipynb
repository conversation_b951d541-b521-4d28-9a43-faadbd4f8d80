{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf1312de", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\hackathon_project\\backend\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["All libraries imported successfully.\n", "Downloading dataset from KaggleHub...\n", "Dataset downloaded to: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\leoscode\\wound-segmentation-images\\versions\\1\n", "Found 2208 training images and masks.\n", "TensorFlow data pipeline created successfully.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"functional\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"functional\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)        </span>┃<span style=\"font-weight: bold\"> Output Shape      </span>┃<span style=\"font-weight: bold\">    Param # </span>┃<span style=\"font-weight: bold\"> Connected to      </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━┩\n", "│ input_layer         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                 │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)                │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">448</span> │ input_layer[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ max_pooling2d       │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ conv2d[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)      │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │      <span style=\"color: #00af00; text-decoration-color: #00af00\">4,640</span> │ max_pooling2d[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]… │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ max_pooling2d_1     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ conv2d_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)      │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │ max_pooling2d_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_transpose    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │      <span style=\"color: #00af00; text-decoration-color: #00af00\">8,224</span> │ conv2d_2[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2DTranspose</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ concatenate         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ conv2d_transpose… │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Concatenate</span>)       │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │ conv2d_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_3 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │     <span style=\"color: #00af00; text-decoration-color: #00af00\">18,464</span> │ concatenate[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_transpose_1  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,064</span> │ conv2d_3[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2DTranspose</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ concatenate_1       │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ conv2d_transpose… │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Concatenate</span>)       │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │ conv2d[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_4 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │      <span style=\"color: #00af00; text-decoration-color: #00af00\">4,624</span> │ concatenate_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]… │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_5 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │         <span style=\"color: #00af00; text-decoration-color: #00af00\">17</span> │ conv2d_4[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n", "│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)                │            │                   │\n", "└─────────────────────┴───────────────────┴────────────┴───────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m   Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to     \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━┩\n", "│ input_layer         │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ -                 │\n", "│ (\u001b[38;5;33mInputLayer\u001b[0m)        │ \u001b[38;5;34m3\u001b[0m)                │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)     │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │        \u001b[38;5;34m448\u001b[0m │ input_layer[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n", "│                     │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ max_pooling2d       │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ conv2d[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "│ (\u001b[38;5;33mMaxPooling2D\u001b[0m)      │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │      \u001b[38;5;34m4,640\u001b[0m │ max_pooling2d[\u001b[38;5;34m0\u001b[0m]… │\n", "│                     │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ max_pooling2d_1     │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ conv2d_1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n", "│ (\u001b[38;5;33mMaxPooling2D\u001b[0m)      │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_2 (\u001b[38;5;33mConv2D\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │     \u001b[38;5;34m18,496\u001b[0m │ max_pooling2d_1[\u001b[38;5;34m…\u001b[0m │\n", "│                     │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_transpose    │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │      \u001b[38;5;34m8,224\u001b[0m │ conv2d_2[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n", "│ (\u001b[38;5;33mConv2DTranspose\u001b[0m)   │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ concatenate         │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ conv2d_transpose… │\n", "│ (\u001b[38;5;33mConcatenate\u001b[0m)       │ \u001b[38;5;34m64\u001b[0m)               │            │ conv2d_1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_3 (\u001b[38;5;33mConv2D\u001b[0m)   │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │     \u001b[38;5;34m18,464\u001b[0m │ concatenate[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n", "│                     │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_transpose_1  │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │      \u001b[38;5;34m2,064\u001b[0m │ conv2d_3[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n", "│ (\u001b[38;5;33mConv2DTranspose\u001b[0m)   │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ concatenate_1       │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ conv2d_transpose… │\n", "│ (\u001b[38;5;33mConcatenate\u001b[0m)       │ \u001b[38;5;34m32\u001b[0m)               │            │ conv2d[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_4 (\u001b[38;5;33mConv2D\u001b[0m)   │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │      \u001b[38;5;34m4,624\u001b[0m │ concatenate_1[\u001b[38;5;34m0\u001b[0m]… │\n", "│                     │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n", "├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n", "│ conv2d_5 (\u001b[38;5;33mConv2D\u001b[0m)   │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │         \u001b[38;5;34m17\u001b[0m │ conv2d_4[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n", "│                     │ \u001b[38;5;34m1\u001b[0m)                │            │                   │\n", "└─────────────────────┴───────────────────┴────────────┴───────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">56,977</span> (222.57 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m56,977\u001b[0m (222.57 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">56,977</span> (222.57 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m56,977\u001b[0m (222.57 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Starting model training for the MVP...\n", "Epoch 1/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m33s\u001b[0m 224ms/step - accuracy: 0.9531 - loss: 0.2037\n", "Epoch 2/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 225ms/step - accuracy: 0.9543 - loss: 0.1327\n", "Epoch 3/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 226ms/step - accuracy: 0.9606 - loss: 0.1173\n", "Epoch 4/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 226ms/step - accuracy: 0.9623 - loss: 0.1115\n", "Epoch 5/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 226ms/step - accuracy: 0.9618 - loss: 0.1120\n", "Epoch 6/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 229ms/step - accuracy: 0.9624 - loss: 0.1105\n", "Epoch 7/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 226ms/step - accuracy: 0.9626 - loss: 0.1087\n", "Epoch 8/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 258ms/step - accuracy: 0.9635 - loss: 0.1071\n", "Epoch 9/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9634 - loss: 0.1042\n", "Epoch 10/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m40s\u001b[0m 284ms/step - accuracy: 0.9627 - loss: 0.1093\n", "Epoch 11/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9633 - loss: 0.1056\n", "Epoch 12/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 260ms/step - accuracy: 0.9638 - loss: 0.1024\n", "Epoch 13/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 257ms/step - accuracy: 0.9637 - loss: 0.1045\n", "Epoch 14/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 254ms/step - accuracy: 0.9640 - loss: 0.1009\n", "Epoch 15/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 260ms/step - accuracy: 0.9647 - loss: 0.0997\n", "Epoch 16/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 264ms/step - accuracy: 0.9648 - loss: 0.0990\n", "Epoch 17/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9649 - loss: 0.0965\n", "Epoch 18/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m38s\u001b[0m 268ms/step - accuracy: 0.9655 - loss: 0.0955\n", "Epoch 19/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m38s\u001b[0m 265ms/step - accuracy: 0.9664 - loss: 0.0929\n", "Epoch 20/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m38s\u001b[0m 265ms/step - accuracy: 0.9671 - loss: 0.0911\n", "Epoch 21/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 263ms/step - accuracy: 0.9658 - loss: 0.0939\n", "Epoch 22/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m38s\u001b[0m 272ms/step - accuracy: 0.9664 - loss: 0.0926\n", "Epoch 23/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9668 - loss: 0.0906\n", "Epoch 24/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m46s\u001b[0m 323ms/step - accuracy: 0.9669 - loss: 0.0902\n", "Epoch 25/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 301ms/step - accuracy: 0.9675 - loss: 0.0888\n", "Epoch 26/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 264ms/step - accuracy: 0.9673 - loss: 0.0898\n", "Epoch 27/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 264ms/step - accuracy: 0.9679 - loss: 0.0870\n", "Epoch 28/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m38s\u001b[0m 266ms/step - accuracy: 0.9681 - loss: 0.0868\n", "Epoch 29/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m40s\u001b[0m 283ms/step - accuracy: 0.9677 - loss: 0.0885\n", "Epoch 30/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m298s\u001b[0m 2s/step - accuracy: 0.9672 - loss: 0.0891\n", "Epoch 31/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 263ms/step - accuracy: 0.9683 - loss: 0.0847\n", "Epoch 32/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 264ms/step - accuracy: 0.9676 - loss: 0.0869\n", "Epoch 33/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 263ms/step - accuracy: 0.9690 - loss: 0.0838\n", "Epoch 34/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9687 - loss: 0.0840\n", "Epoch 35/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m131s\u001b[0m 949ms/step - accuracy: 0.9684 - loss: 0.0853\n", "Epoch 36/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m50s\u001b[0m 347ms/step - accuracy: 0.9690 - loss: 0.0824\n", "Epoch 37/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m41s\u001b[0m 292ms/step - accuracy: 0.9688 - loss: 0.0826\n", "Epoch 38/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m39s\u001b[0m 278ms/step - accuracy: 0.9687 - loss: 0.0853\n", "Epoch 39/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 306ms/step - accuracy: 0.9689 - loss: 0.0839\n", "Epoch 40/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 250ms/step - accuracy: 0.9695 - loss: 0.0818\n", "Epoch 41/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m35s\u001b[0m 246ms/step - accuracy: 0.9686 - loss: 0.0844\n", "Epoch 42/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m33s\u001b[0m 233ms/step - accuracy: 0.9696 - loss: 0.0814\n", "Epoch 43/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m33s\u001b[0m 232ms/step - accuracy: 0.9693 - loss: 0.0830\n", "Epoch 44/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m31s\u001b[0m 217ms/step - accuracy: 0.9695 - loss: 0.0806\n", "Epoch 45/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m31s\u001b[0m 220ms/step - accuracy: 0.9700 - loss: 0.0810\n", "Epoch 46/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m30s\u001b[0m 215ms/step - accuracy: 0.9698 - loss: 0.0799\n", "Epoch 47/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m31s\u001b[0m 218ms/step - accuracy: 0.9698 - loss: 0.0806\n", "Epoch 48/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 224ms/step - accuracy: 0.9698 - loss: 0.0806\n", "Epoch 49/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m33s\u001b[0m 237ms/step - accuracy: 0.9699 - loss: 0.0796\n", "Epoch 50/50\n", "\u001b[1m138/138\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m37s\u001b[0m 262ms/step - accuracy: 0.9696 - loss: 0.0814\n", "MVP model training complete!\n", "Model successfully saved as wound_segmentation_model_v1.keras\n"]}], "source": ["import os\n", "import tensorflow as tf\n", "from tensorflow.keras import layers, models\n", "import kagglehub\n", "\n", "print(\"All libraries imported successfully.\")\n", "\n", "# --- 1. <PERSON>OWNLOAD DATASET ---\n", "print(\"Downloading dataset from KaggleHub...\")\n", "path_to_dataset = kagglehub.dataset_download(\"leoscode/wound-segmentation-images\")\n", "print(f\"Dataset downloaded to: {path_to_dataset}\")\n", "\n", "# --- 2. PREPAR<PERSON> FILE PATHS ---\n", "base_path = os.path.join(str(path_to_dataset), 'data_wound_seg')\n", "train_images_dir = os.path.join(base_path, 'train_images')\n", "train_masks_dir = os.path.join(base_path, 'train_masks')\n", "\n", "train_image_files = sorted(os.listdir(train_images_dir))\n", "train_mask_files = sorted(os.listdir(train_masks_dir))\n", "\n", "full_train_image_paths = [os.path.join(train_images_dir, f) for f in train_image_files]\n", "full_train_mask_paths = [os.path.join(train_masks_dir, f) for f in train_mask_files]\n", "print(f\"Found {len(train_image_files)} training images and masks.\")\n", "\n", "# --- 3. BUILD THE DATA PIPELINE ---\n", "IMG_HEIGHT = 224\n", "IMG_WIDTH = 224\n", "BATCH_SIZE = 16\n", "\n", "def parse_image_and_mask(image_path, mask_path):\n", "    image = tf.io.read_file(image_path)\n", "    image = tf.image.decode_png(image, channels=3)\n", "    image = tf.image.convert_image_dtype(image, tf.float32)\n", "    image = tf.image.resize(image, [IMG_HEIGHT, IMG_WIDTH])\n", "\n", "    mask = tf.io.read_file(mask_path)\n", "    mask = tf.image.decode_png(mask, channels=1)\n", "    mask = tf.image.resize(mask, [IMG_HEIGHT, IMG_WIDTH])\n", "    mask = tf.cast(mask > 0.5, dtype=tf.float32)\n", "    return image, mask\n", "\n", "dataset = tf.data.Dataset.from_tensor_slices((full_train_image_paths, full_train_mask_paths))\n", "dataset = dataset.map(parse_image_and_mask, num_parallel_calls=tf.data.AUTOTUNE)\n", "dataset = dataset.shuffle(buffer_size=1000).batch(BATCH_SIZE).prefetch(buffer_size=tf.data.AUTOTUNE)\n", "print(\"TensorFlow data pipeline created successfully.\")\n", "\n", "# --- 4. BUILD THE U-NET MODEL ---\n", "def build_unet_model(input_shape=(224, 224, 3)):\n", "    inputs = layers.Input(input_shape)\n", "    # Encoder\n", "    c1 = layers.Conv2D(16, (3, 3), activation='relu', padding='same')(inputs)\n", "    p1 = layers.MaxPooling2D((2, 2))(c1)\n", "    c2 = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(p1)\n", "    p2 = layers.MaxPooling2D((2, 2))(c2)\n", "    # Bottleneck\n", "    b = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(p2)\n", "    # Decoder\n", "    u2 = layers.Conv2DTranspose(32, (2, 2), strides=(2, 2), padding='same')(b)\n", "    u2 = layers.concatenate([u2, c2])\n", "    c7 = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(u2)\n", "    u3 = layers.Conv2DTranspose(16, (2, 2), strides=(2, 2), padding='same')(c7)\n", "    u3 = layers.concatenate([u3, c1])\n", "    c8 = layers.Conv2D(16, (3, 3), activation='relu', padding='same')(u3)\n", "    outputs = layers.Conv2D(1, (1, 1), activation='sigmoid')(c8)\n", "    model = models.Model(inputs=[inputs], outputs=[outputs])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "\n", "model = build_unet_model()\n", "model.summary()\n", "\n", "# --- 5. T<PERSON><PERSON> THE MVP MODEL ---\n", "print(\"\\nStarting model training for the MVP...\")\n", "EPOCHS = 50+\n", "history = model.fit(dataset, epochs=EPOCHS)\n", "print(\"MVP model training complete!\")\n", "\n", "model.save('wound_segmentation_model_v1.keras')\n", "print(\"Model successfully saved as wound_segmentation_model_v1.keras\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
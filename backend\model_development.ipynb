import os
import tensorflow as tf
from tensorflow.keras import layers, models
import kagglehub
print(tf.config.list_physical_devices('GPU'))

print("All libraries imported successfully.")

# --- 1. DOWNLOAD DATASET ---
print("Downloading dataset from KaggleHub...")
path_to_dataset = kagglehub.dataset_download("leoscode/wound-segmentation-images")
print(f"Dataset downloaded to: {path_to_dataset}")

# --- 2. PREPARE FILE PATHS ---
base_path = os.path.join(str(path_to_dataset), 'data_wound_seg')
train_images_dir = os.path.join(base_path, 'train_images')
train_masks_dir = os.path.join(base_path, 'train_masks')

train_image_files = sorted(os.listdir(train_images_dir))
train_mask_files = sorted(os.listdir(train_masks_dir))

full_train_image_paths = [os.path.join(train_images_dir, f) for f in train_image_files]
full_train_mask_paths = [os.path.join(train_masks_dir, f) for f in train_mask_files]
print(f"Found {len(train_image_files)} training images and masks.")

# --- 3. BUILD THE DATA PIPELINE ---
IMG_HEIGHT = 224
IMG_WIDTH = 224
BATCH_SIZE = 16

def parse_image_and_mask(image_path, mask_path):
    image = tf.io.read_file(image_path)
    image = tf.image.decode_png(image, channels=3)
    image = tf.image.convert_image_dtype(image, tf.float32)
    image = tf.image.resize(image, [IMG_HEIGHT, IMG_WIDTH])

    mask = tf.io.read_file(mask_path)
    mask = tf.image.decode_png(mask, channels=1)
    mask = tf.image.resize(mask, [IMG_HEIGHT, IMG_WIDTH])
    mask = tf.cast(mask > 0.5, dtype=tf.float32)
    return image, mask

dataset = tf.data.Dataset.from_tensor_slices((full_train_image_paths, full_train_mask_paths))
dataset = dataset.map(parse_image_and_mask, num_parallel_calls=tf.data.AUTOTUNE)
dataset = dataset.shuffle(buffer_size=1000).batch(BATCH_SIZE).prefetch(buffer_size=tf.data.AUTOTUNE)
print("TensorFlow data pipeline created successfully.")

# --- 4. BUILD THE U-NET MODEL ---
def build_unet_model(input_shape=(224, 224, 3)):
    inputs = layers.Input(input_shape)
    # Encoder
    c1 = layers.Conv2D(16, (3, 3), activation='relu', padding='same')(inputs)
    p1 = layers.MaxPooling2D((2, 2))(c1)
    c2 = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(p1)
    p2 = layers.MaxPooling2D((2, 2))(c2)
    # Bottleneck
    b = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(p2)
    # Decoder
    u2 = layers.Conv2DTranspose(32, (2, 2), strides=(2, 2), padding='same')(b)
    u2 = layers.concatenate([u2, c2])
    c7 = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(u2)
    u3 = layers.Conv2DTranspose(16, (2, 2), strides=(2, 2), padding='same')(c7)
    u3 = layers.concatenate([u3, c1])
    c8 = layers.Conv2D(16, (3, 3), activation='relu', padding='same')(u3)
    outputs = layers.Conv2D(1, (1, 1), activation='sigmoid')(c8)
    model = models.Model(inputs=[inputs], outputs=[outputs])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

model = build_unet_model()
model.summary()

# --- 5. TRAIN THE MVP MODEL ---
print("\nStarting model training for the MVP...")
EPOCHS = 
history = model.fit(dataset, epochs=EPOCHS)
print("MVP model training complete!")

model.save('wound_segmentation_model_v1.keras')
print("Model successfully saved as wound_segmentation_model_v1.keras")
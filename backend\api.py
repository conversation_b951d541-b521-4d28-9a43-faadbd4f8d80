# Updated Backend API with CORS and Peri-Wound Analysis
import tensorflow as tf
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
from PIL import Image
import io
import base64
import sqlite3
import time
import os
import cv2 # <-- ADDED THIS IMPORT

app = Flask(__name__)
CORS(app)

DB_FILE = 'wound_data.db'

# --- Database Initialization ---
def init_db():
    if not os.path.exists(DB_FILE):
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE measurements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT NOT NULL,
                timestamp REAL NOT NULL,
                area REAL NOT NULL
            )
        ''')
        conn.commit()
        conn.close()
        print("Database initialized.")

init_db()

# --- NEW: Peri-Wound Analysis Function ---
def analyze_peri_wound(original_image_np, binary_mask):
    """
    Analyzes the peri-wound area for redness and returns a health status.

    Args:
        original_image_np (np.array): The original RGB image as a NumPy array.
        binary_mask (np.array): The single-channel binary mask (0s and 255s).

    Returns:
        dict: A dictionary containing the status and redness score.
    """
    try:
        # 1. Define the kernel for dilation. This determines the thickness of our "donut".
        kernel = np.ones((15, 15), np.uint8)

        # 2. Dilate the mask to create an enlarged version.
        dilated_mask = cv2.dilate(binary_mask, kernel, iterations=1)

        # 3. Create the peri-wound mask by subtracting the original mask.
        peri_wound_mask = cv2.subtract(dilated_mask, binary_mask)

        # 4. Calculate the average color of the peri-wound region.
        # We need to convert the original image to BGR for OpenCV's cv2.mean function.
        original_image_bgr = cv2.cvtColor(original_image_np, cv2.COLOR_RGB2BGR)
        avg_color_bgr = cv2.mean(original_image_bgr, mask=peri_wound_mask)
        avg_b, avg_g, avg_r, _ = avg_color_bgr
        
        # 5. Create a simple redness score.
        # Ratio of Red to the average of Green/Blue. Add epsilon to avoid division by zero.
        if (avg_g + avg_b) > 0:
            redness_score = avg_r / ((avg_g + avg_b) / 2 + 1e-5)
        else:
            redness_score = avg_r
            
        # 6. Determine the status based on thresholds.
        status = "Normal"
        if redness_score > 1.2:
            status = "Caution"
        if redness_score > 1.5:
            status = "High Alert"

        return {
            "status": status,
            "redness_score": round(redness_score, 2)
        }

    except Exception as e:
        print(f"Error in peri-wound analysis: {e}")
        return {
            "status": "Error",
            "redness_score": 0
        }

# --- Load All Models ---
try:
    model_segmentation = tf.keras.models.load_model('wound_segmentation_model_v1.keras')
    print("Segmentation model loaded successfully!")
except Exception as e:
    print(f"Error loading segmentation model: {e}")
    model_segmentation = None

try:
    model_classifier = tf.keras.models.load_model('tissue_classifier_model_v1.keras')
    class_names = ['Healthy Tissue', 'Infected/Necrotic'] 
    print("Classifier model loaded successfully!")
except Exception as e:
    print(f"Error loading classifier model: {e}")
    model_classifier = None

# --- UPDATED: Prediction Endpoint ---
@app.route('/predict', methods=['POST'])
def predict():
    if model_segmentation is None or model_classifier is None:
        return jsonify({'error': 'One or more models are not loaded'}), 500
    
    if 'file' not in request.files: return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    patient_id = request.form.get('patient_id', 'default_patient')

    try:
        image = Image.open(file.stream).convert('RGB')
        image_resized = image.resize((224, 224))
        image_array = np.array(image_resized)
        
        # Prepare image batch for models (normalized)
        image_normalized_for_model = image_array / 255.0
        image_batch = np.expand_dims(image_normalized_for_model, axis=0)

        # 1. Segmentation Prediction
        predicted_mask = model_segmentation.predict(image_batch)
        predicted_mask_binary_float = (predicted_mask > 0.5).astype(np.float32)
        wound_area = np.sum(predicted_mask_binary_float) / (224 * 224) * 100

        # 2. Tissue Classification Prediction
        predictions = model_classifier.predict(image_batch)
        scores = tf.nn.softmax(predictions[0])
        tissue_results = {class_names[i]: float(scores[i]) * 100 for i in range(len(class_names))}
        
        # 3. Peri-Wound Analysis
        mask_for_cv2 = (predicted_mask_binary_float.squeeze() * 255).astype(np.uint8)
        peri_wound_result = analyze_peri_wound(image_array, mask_for_cv2)
        
        # 4. Save to Database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute("INSERT INTO measurements (patient_id, timestamp, area) VALUES (?, ?, ?)",
                       (patient_id, time.time(), wound_area))
        conn.commit()
        conn.close()

        # Prepare mask image for sending
        mask_image_display = (predicted_mask_binary_float * 255).astype(np.uint8)
        mask_image = Image.fromarray(mask_image_display.squeeze(), mode='L')
        buffered = io.BytesIO()
        mask_image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')

        return jsonify({
            'mask': img_str,
            'area': round(wound_area, 2),
            'tissue_analysis': tissue_results,
            'peri_wound_analysis': peri_wound_result
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# --- History Endpoint (Unchanged) ---
@app.route('/history', methods=['GET'])
def history():
    patient_id = request.args.get('patient_id')
    if not patient_id: return jsonify({'error': 'Patient ID is required'}), 400
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    cursor.execute("SELECT timestamp, area FROM measurements WHERE patient_id = ? ORDER BY timestamp ASC", (patient_id,))
    rows = cursor.fetchall()
    conn.close()
    
    history_data = [{'timestamp': r[0], 'area': r[1]} for r in rows]
    return jsonify(history_data)

# --- Main Execution (Unchanged) ---
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
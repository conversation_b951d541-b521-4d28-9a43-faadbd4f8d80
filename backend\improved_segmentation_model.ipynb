{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1dc544d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 1: Downloading and preparing the new FUSC dataset...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\hackathon_project\\backend\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/datasets/download/ipunguhbpwt/fusc-data?dataset_version_number=1...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 237M/237M [01:34<00:00, 2.62MB/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting files...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dataset zip file downloaded successfully to: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\ipunguhbpwt\\fusc-data\\versions\\1\n", "An error occurred: [<PERSON><PERSON><PERSON> 13] Permission denied: 'C:\\\\Users\\\\<USER>\\\\.cache\\\\kagglehub\\\\datasets\\\\ipunguhbpwt\\\\fusc-data\\\\versions\\\\1'\n"]}], "source": ["import kagglehub\n", "import os\n", "import zipfile\n", "\n", "print(\"Step 1: Downloading and preparing the new FUSC dataset...\")\n", "\n", "# --- Configuration ---\n", "# This is the new, better dataset for segmentation\n", "NEW_DATASET_ID = \"ipunguhbpwt/fusc-data\"\n", "\n", "# --- Download the Dataset ---\n", "try:\n", "    path_to_new_dataset_zip = kagglehub.dataset_download(NEW_DATASET_ID)\n", "    print(f\"Dataset zip file downloaded successfully to: {path_to_new_dataset_zip}\")\n", "\n", "    # --- Unzip the Dataset ---\n", "    unzip_path = os.path.join('D:/hackathon_project/backend', 'new_dataset')\n", "    os.makedirs(unzip_path, exist_ok=True)\n", "    \n", "    with zipfile.ZipFile(path_to_new_dataset_zip, 'r') as zip_ref:\n", "        zip_ref.extractall(unzip_path)\n", "    print(f\"Dataset successfully unzipped to: {unzip_path}\")\n", "\n", "    # --- Verify the Folder Structure ---\n", "    # We need to find the paths to the training images and masks\n", "    # This might require some exploration of the unzipped folder.\n", "    # Let's assume a common structure for now.\n", "    train_images_path = os.path.join(unzip_path, 'train_images') # Adjust if name is different\n", "    train_masks_path = os.path.join(unzip_path, 'train_masks')   # Adjust if name is different\n", "\n", "    if os.path.exists(train_images_path) and os.path.exists(train_masks_path):\n", "        num_images = len(os.listdir(train_images_path))\n", "        num_masks = len(os.listdir(train_masks_path))\n", "        \n", "        print(\"\\n--- SUCCESS! ---\")\n", "        print(\"Dataset structure seems correct.\")\n", "        print(f\"Found {num_images} training images.\")\n", "        print(f\"Found {num_masks} training masks.\")\n", "        \n", "        if num_images == num_masks:\n", "            print(\"The number of images and masks match. Ready for the next step.\")\n", "        else:\n", "            print(\"Warning: The number of images and masks do not match. Please check the dataset.\")\n", "    else:\n", "        print(\"\\n--- WARNING ---\")\n", "        print(\"Could not automatically find the 'train_images' and 'train_masks' folders.\")\n", "        print(\"Please manually check the unzipped folder at:\")\n", "        print(unzip_path)\n", "        print(\"And update the paths in the next script accordingly.\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
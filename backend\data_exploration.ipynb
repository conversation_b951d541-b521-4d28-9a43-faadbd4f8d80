{"cells": [{"cell_type": "code", "execution_count": 7, "id": "6006d480", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting ka<PERSON><PERSON><PERSON>\n", "  Downloading kagglehub-0.3.13-py3-none-any.whl.metadata (38 kB)\n", "Requirement already satisfied: packaging in .\\venv\\lib\\site-packages (from kagglehub) (25.0)\n", "Collecting pyyaml (from ka<PERSON><PERSON><PERSON>)\n", "  Downloading PyYAML-6.0.2-cp313-cp313-win_amd64.whl.metadata (2.1 kB)\n", "Collecting requests (from ka<PERSON><PERSON><PERSON>)\n", "  Downloading requests-2.32.5-py3-none-any.whl.metadata (4.9 kB)\n", "Collecting tqdm (from kagglehub)\n", "  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)\n", "Collecting charset_normalizer<4,>=2 (from requests->kagglehub)\n", "  Downloading charset_normalizer-3.4.3-cp313-cp313-win_amd64.whl.metadata (37 kB)\n", "Collecting idna<4,>=2.5 (from requests->kagglehub)\n", "  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting urllib3<3,>=1.21.1 (from requests->kagglehub)\n", "  Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting certifi>=2017.4.17 (from requests->kagglehub)\n", "  Downloading certifi-2025.8.3-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: colorama in .\\venv\\lib\\site-packages (from tqdm->kagglehub) (0.4.6)\n", "Downloading kagglehub-0.3.13-py3-none-any.whl (68 kB)\n", "Downloading PyYAML-6.0.2-cp313-cp313-win_amd64.whl (156 kB)\n", "Downloading requests-2.32.5-py3-none-any.whl (64 kB)\n", "Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)\n", "Downloading certifi-2025.8.3-py3-none-any.whl (161 kB)\n", "Downloading charset_normalizer-3.4.3-cp313-cp313-win_amd64.whl (107 kB)\n", "Downloading idna-3.10-py3-none-any.whl (70 kB)\n", "Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)\n", "Installing collected packages: urllib3, tqdm, pyyaml, idna, charset_normalizer, certifi, requests, kagglehub\n", "Successfully installed certifi-2025.8.3 charset_normalizer-3.4.3 idna-3.10 kagglehub-0.3.13 pyyaml-6.0.2 requests-2.32.5 tqdm-4.67.1 urllib3-2.5.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install kagglehub"]}, {"cell_type": "code", "execution_count": 5, "id": "6fe42afb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting mat<PERSON><PERSON><PERSON><PERSON>\n", "  Downloading matplotlib-3.10.6-cp313-cp313-win_amd64.whl.metadata (11 kB)\n", "Collecting contourpy>=1.0.1 (from matplotlib)\n", "  Downloading contourpy-1.3.3-cp313-cp313-win_amd64.whl.metadata (5.5 kB)\n", "Collecting cycler>=0.10 (from matplotlib)\n", "  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting fonttools>=4.22.0 (from matplotlib)\n", "  Downloading fonttools-4.60.0-cp313-cp313-win_amd64.whl.metadata (113 kB)\n", "Collecting kiwisolver>=1.3.1 (from matplotlib)\n", "  Downloading kiwisolver-1.4.9-cp313-cp313-win_amd64.whl.metadata (6.4 kB)\n", "Requirement already satisfied: numpy>=1.23 in .\\venv\\lib\\site-packages (from matplotlib) (2.2.6)\n", "Requirement already satisfied: packaging>=20.0 in .\\venv\\lib\\site-packages (from matplotlib) (25.0)\n", "Collecting pillow>=8 (from matplotlib)\n", "  Using cached pillow-11.3.0-cp313-cp313-win_amd64.whl.metadata (9.2 kB)\n", "Collecting pyparsing>=2.3.1 (from matplotlib)\n", "  Downloading pyparsing-3.2.5-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: python-dateutil>=2.7 in .\\venv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in .\\venv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Downloading matplotlib-3.10.6-cp313-cp313-win_amd64.whl (8.1 MB)\n", "   ---------------------------------------- 0.0/8.1 MB ? eta -:--:--\n", "   --------- ------------------------------ 1.8/8.1 MB 10.8 MB/s eta 0:00:01\n", "   ------------------- -------------------- 3.9/8.1 MB 10.5 MB/s eta 0:00:01\n", "   ------------------------------ --------- 6.3/8.1 MB 10.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 8.1/8.1 MB 10.7 MB/s eta 0:00:00\n", "Downloading contourpy-1.3.3-cp313-cp313-win_amd64.whl (226 kB)\n", "Using cached cycler-0.12.1-py3-none-any.whl (8.3 kB)\n", "Downloading fonttools-4.60.0-cp313-cp313-win_amd64.whl (2.3 MB)\n", "   ---------------------------------------- 0.0/2.3 MB ? eta -:--:--\n", "   ---------------------------------------- 2.3/2.3 MB 12.5 MB/s eta 0:00:00\n", "Downloading kiwisolver-1.4.9-cp313-cp313-win_amd64.whl (73 kB)\n", "Using cached pillow-11.3.0-cp313-cp313-win_amd64.whl (7.0 MB)\n", "Downloading pyparsing-3.2.5-py3-none-any.whl (113 kB)\n", "Installing collected packages: pyparsing, pillow, kiwisolver, fonttools, cycler, contourpy, matplotlib\n", "Successfully installed contourpy-1.3.3 cycler-0.12.1 fonttools-4.60.0 kiwisolver-1.4.9 matplotlib-3.10.6 pillow-11.3.0 pyparsing-3.2.5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": 2, "id": "6d8b4020", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting opencv-python\n", "  Downloading opencv_python-*********-cp37-abi3-win_amd64.whl.metadata (19 kB)\n", "Collecting numpy<2.3.0,>=2 (from opencv-python)\n", "  Downloading numpy-2.2.6-cp313-cp313-win_amd64.whl.metadata (60 kB)\n", "Downloading opencv_python-*********-cp37-abi3-win_amd64.whl (39.0 MB)\n", "   ---------------------------------------- 0.0/39.0 MB ? eta -:--:--\n", "   - -------------------------------------- 1.0/39.0 MB 6.3 MB/s eta 0:00:07\n", "   -- ------------------------------------- 2.6/39.0 MB 7.4 MB/s eta 0:00:05\n", "   ---- ----------------------------------- 4.5/39.0 MB 7.9 MB/s eta 0:00:05\n", "   ------ --------------------------------- 6.0/39.0 MB 8.2 MB/s eta 0:00:05\n", "   -------- ------------------------------- 8.7/39.0 MB 9.0 MB/s eta 0:00:04\n", "   ----------- ---------------------------- 10.7/39.0 MB 9.4 MB/s eta 0:00:04\n", "   ------------- -------------------------- 13.1/39.0 MB 9.6 MB/s eta 0:00:03\n", "   --------------- ------------------------ 15.5/39.0 MB 9.8 MB/s eta 0:00:03\n", "   ------------------ --------------------- 18.1/39.0 MB 10.1 MB/s eta 0:00:03\n", "   -------------------- ------------------- 20.2/39.0 MB 10.2 MB/s eta 0:00:02\n", "   ----------------------- ---------------- 22.5/39.0 MB 10.3 MB/s eta 0:00:02\n", "   ------------------------- -------------- 24.9/39.0 MB 10.4 MB/s eta 0:00:02\n", "   --------------------------- ------------ 27.3/39.0 MB 10.4 MB/s eta 0:00:02\n", "   ------------------------------ --------- 29.6/39.0 MB 10.5 MB/s eta 0:00:01\n", "   -------------------------------- ------- 32.0/39.0 MB 10.6 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 34.3/39.0 MB 10.6 MB/s eta 0:00:01\n", "   ------------------------------------- -- 36.7/39.0 MB 10.7 MB/s eta 0:00:01\n", "   ---------------------------------------  38.8/39.0 MB 10.8 MB/s eta 0:00:01\n", "   ---------------------------------------- 39.0/39.0 MB 10.6 MB/s eta 0:00:00\n", "Downloading numpy-2.2.6-cp313-cp313-win_amd64.whl (12.6 MB)\n", "   ---------------------------------------- 0.0/12.6 MB ? eta -:--:--\n", "   ------ --------------------------------- 2.1/12.6 MB 11.0 MB/s eta 0:00:01\n", "   -------------- ------------------------- 4.5/12.6 MB 10.7 MB/s eta 0:00:01\n", "   -------------------- ------------------- 6.6/12.6 MB 10.9 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 8.9/12.6 MB 11.1 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 11.3/12.6 MB 11.0 MB/s eta 0:00:01\n", "   ---------------------------------------- 12.6/12.6 MB 10.8 MB/s eta 0:00:00\n", "Installing collected packages: numpy, opencv-python\n", "Successfully installed numpy-2.2.6 opencv-python-*********\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install opencv-python"]}, {"cell_type": "code", "execution_count": 8, "id": "d02bffe7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\hackathon_project\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/datasets/download/leoscode/wound-segmentation-images?dataset_version_number=1...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 665M/665M [01:03<00:00, 11.0MB/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting files...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dataset downloaded to: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\leoscode\\wound-segmentation-images\\versions\\1\n", "\n", "Contents of the dataset folder:\n", "data_wound_seg\n"]}], "source": ["import kagglehub\n", "import os # Import the os library to interact with the file system\n", "\n", "# 1. Download the dataset and get the path\n", "# It will be downloaded to a cache folder like C:\\Users\\<USER>\\.Kaggle\\datasets\\...\n", "path_to_dataset = kagglehub.dataset_download(\"leoscode/wound-segmentation-images\")\n", "\n", "print(f\"Dataset downloaded to: {path_to_dataset}\")\n", "\n", "# 2. Use the returned path to see the contents\n", "print(\"\\nContents of the dataset folder:\")\n", "# os.listdir() lists all files and folders in a given path\n", "file_list = os.listdir(path_to_dataset)\n", "for f in file_list:\n", "    print(f)"]}, {"cell_type": "code", "execution_count": 9, "id": "d34bedf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading image: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\leoscode\\wound-segmentation-images\\versions\\1\\data_wound_seg\\train_images\\fusc_0002.png\n", "Loading mask: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\leoscode\\wound-segmentation-images\\versions\\1\\data_wound_seg\\train_masks\\fusc_0002.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# This is the path we got from the kagglehub download\n", "# Note: You might need to add '\\\\data_wound_seg' at the end if the files are in a subfolder\n", "path_to_dataset = r'C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\leoscode\\wound-segmentation-images\\versions\\1'\n", "\n", "# Let's define the paths to the specific image and mask folders\n", "train_images_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_images')\n", "train_masks_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_masks')\n", "\n", "# 1. Get a list of all image filenames\n", "all_image_files = os.listdir(train_images_path)\n", "sample_image_file = all_image_files[0] # Let's just pick the first one\n", "\n", "# 2. Construct the full paths for the image and its corresponding mask\n", "full_image_path = os.path.join(train_images_path, sample_image_file)\n", "# The mask usually has the same name as the image\n", "full_mask_path = os.path.join(train_masks_path, sample_image_file) \n", "\n", "print(f\"Loading image: {full_image_path}\")\n", "print(f\"Loading mask: {full_mask_path}\")\n", "\n", "# 3. Load the image and the mask\n", "image = cv2.imread(full_image_path)\n", "image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB) # Convert to RGB for display\n", "\n", "# Masks are usually grayscale, so we load it in grayscale mode\n", "mask = cv2.imread(full_mask_path, cv2.IMREAD_GRAYSCALE) \n", "\n", "# 4. Display them to verify\n", "plt.figure(figsize=(10, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Wound Image\")\n", "plt.imshow(image)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Wound Mask\")\n", "plt.imshow(mask, cmap='gray') # Use a gray colormap for the mask\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
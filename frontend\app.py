import streamlit as st
from PIL import Image
import requests
import io
import base64
import pandas as pd
import datetime

# --- Page Configuration ---
st.set_page_config(
    page_title="WoundCare AI",
    page_icon="🩹",
    layout="wide"
)

# --- Custom CSS to mimic your design ---
st.markdown("""
<style>
    /* Main App Styling */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #1f2937;
    }
    
    /* Main content area */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Card Styling */
    .st-emotion-cache-183lzff, .st-emotion-cache-1r6slb0 { 
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }
    
    /* Header <PERSON>yl<PERSON> */
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .logo-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .logo {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #2563eb, #1e40af);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 20px;
    }
    
    .app-title {
        font-size: 28px;
        font-weight: 700;
        color: #111827;
    }
    
    /* Buttons */
    .stButton>button {
        border-radius: 12px;
        font-weight: 600;
    }
</style>
""", unsafe_allow_html=True)


# --- Backend URLs ---
BACKEND_URL_PREDICT = 'http://127.0.0.1:5000/predict'
BACKEND_URL_HISTORY = 'http://127.0.0.1:5000/history'

# --- Header ---
st.markdown("""
<div class="header">
    <div class="logo-section">
        <div class="logo">W+</div>
        <div class="app-title">WoundCare AI Dashboard</div>
    </div>
</div>
""", unsafe_allow_html=True)


# --- Main Application Layout ---
col_main_left, col_main_right = st.columns([1.5, 2], gap="large")

with col_main_left:
    st.header("📋 Patient Input")
    patient_id = st.text_input("Patient ID", "patient_001", help="Enter a unique ID for the patient.")
    uploaded_file = st.file_uploader("Upload Wound Image", type=["jpg", "jpeg", "png"])
    
    analyze_button = st.button("Analyze Wound", use_container_width=True, type="primary", disabled=(uploaded_file is None))

    if uploaded_file:
        st.image(uploaded_file, caption='Uploaded Image for Analysis', use_column_width=True)

with col_main_right:
    st.header("📊 AI Analysis & Results")

    if analyze_button:
        with st.spinner('AI is analyzing the wound... This may take a moment.'):
            try:
                files = {'file': (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
                data = {'patient_id': patient_id}
                response = requests.post(BACKEND_URL_PREDICT, files=files, data=data)

                if response.status_code == 200:
                    st.session_state.analysis_results = response.json()
                    st.session_state.current_patient_id = patient_id
                else:
                    st.error(f"Error from server: {response.status_code} - {response.json().get('error', 'Unknown error')}")
            
            except requests.exceptions.RequestException as e:
                st.error(f"Connection Error: Could not connect to the backend. Please ensure the Flask server (api.py) is running.")

    if 'analysis_results' in st.session_state and st.session_state.current_patient_id == patient_id:
        results = st.session_state.analysis_results
        
        res_col1, res_col2 = st.columns(2)
        
        with res_col1:
            st.subheader("Predicted Wound Mask")
            mask_str = results.get('mask')
            mask_image = Image.open(io.BytesIO(base64.b64decode(mask_str)))
            st.image(mask_image, use_column_width=True)

        with res_col2:
            st.subheader("Key Metrics")
            area = results.get('area')
            st.metric(label="Wound Area (% of image)", value=f"{area}%")
            
            # --- Display Peri-Wound Results (The Unique Feature!) ---
            peri_wound_analysis = results.get('peri_wound_analysis')
            if peri_wound_analysis:
                st.subheader("🔬 Peri-Wound Health Index")
                status = peri_wound_analysis.get("status")
                score = peri_wound_analysis.get("redness_score")

                if status == "High Alert":
                    st.error(f"**Status: {status}**")
                elif status == "Caution":
                    st.warning(f"**Status: {status}**")
                else:
                    st.success(f"**Status: {status}**")
                
                st.metric(label="Redness Score", value=f"{score}")
                st.caption("Measures inflammation in the surrounding skin.")

        st.subheader("Tissue Analysis (%)")
        tissue_analysis = results.get('tissue_analysis')
        if tissue_analysis:
            st.bar_chart(tissue_analysis)

        # --- Fetch and display history chart ---
        st.subheader(f"📈 Healing History for {patient_id}")
        try:
            history_response = requests.get(BACKEND_URL_HISTORY, params={'patient_id': patient_id})
            if history_response.status_code == 200:
                history_data = history_response.json()
                if history_data:
                    df = pd.DataFrame(history_data)
                    df['date'] = pd.to_datetime(df['timestamp'], unit='s')
                    df.set_index('date', inplace=True)
                    st.area_chart(df['area'])
                else:
                    st.info("This is the first scan for this patient. Future analyses will build the healing graph.")
            else:
                st.error("Could not fetch healing history.")
        except requests.exceptions.RequestException:
             st.error("Could not fetch healing history. Backend server may be down.")
    else:
        st.info("Upload an image and click 'Analyze Wound' to see the results here.")
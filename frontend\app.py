import streamlit as st
from PIL import Image
import requests
import io
import base64
import pandas as pd
import datetime
from streamlit_option_menu import option_menu

# --- Page Configuration ---
st.set_page_config(
    page_title="WoundCare AI",
    page_icon="🩹",
    layout="wide"
)

# --- Custom CSS to mimic the HTML design ---
st.markdown("""
<style>
    /* Main App Styling */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    /* Main content area */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Card Styling */
    .st-emotion-cache-183lzff { /* This is a common class for Streamlit containers */
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    /* Header Styling */
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .logo-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .logo {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #2563eb, #1e40af);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 20px;
    }
    
    .app-title {
        font-size: 28px;
        font-weight: 700;
        color: #111827;
    }
    
    .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: rgba(22, 163, 74, 0.1);
        border: 1px solid #16a34a;
        border-radius: 20px;
        color: #16a34a;
        font-size: 14px;
        font-weight: 500;
    }
</style>
""", unsafe_allow_html=True)


# --- Backend URLs ---
BACKEND_URL_PREDICT = 'http://127.0.0.1:5000/predict'
BACKEND_URL_HISTORY = 'http://127.0.0.1:5000/history'

# --- Header ---
st.markdown("""
<div class="header">
    <div class="logo-section">
        <div class="logo">W+</div>
        <div class="app-title">WoundCare AI</div>
    </div>
    <div class="status-indicator">
        AI System Online
    </div>
</div>
""", unsafe_allow_html=True)


# --- Main Application ---
col1, col2, col3 = st.columns([1, 2, 1])

# --- Left Panel ---
with col1:
    st.subheader("👤 Patient & Controls")
    patient_id = st.text_input("Patient ID", "patient_001")
    uploaded_file = st.file_uploader("Upload Wound Image", type=["jpg", "jpeg", "png"])
    
    analyze_button = st.button("Analyze Wound", use_container_width=True, type="primary", disabled=(uploaded_file is None))

    if 'history_data' in st.session_state:
        st.subheader("📈 Healing Score")
        history = st.session_state.history_data
        if len(history) > 1:
            latest_area = history[-1]['area']
            previous_area = history[-2]['area']
            change = ((latest_area - previous_area) / previous_area) * 100 if previous_area > 0 else 0
            st.metric(label="Change Since Last Scan", value=f"{change:.1f}%", delta=f"{change:.1f}%")
        else:
            st.info("Upload another scan to see healing score.")


# --- Center Panel ---
with col2:
    st.subheader("📸 AI Wound Analysis")
    if uploaded_file is None:
        st.info("Please upload an image to begin analysis.")
    else:
        st.image(uploaded_file, caption='Uploaded Image for Analysis', use_column_width=True)

# --- Right Panel ---
with col3:
    st.subheader("📊 Results & History")

    if analyze_button:
        with st.spinner('Analyzing wound... This may take a moment.'):
            try:
                files = {'file': (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
                data = {'patient_id': patient_id}
                response = requests.post(BACKEND_URL_PREDICT, files=files, data=data)

                if response.status_code == 200:
                    analysis_results = response.json()
                    st.session_state.analysis_results = analysis_results
                else:
                    st.error(f"Error from server: {response.status_code} - {response.json().get('error', 'Unknown error')}")
            
            except requests.exceptions.RequestException as e:
                st.error(f"Connection Error: Is the backend server running?")

    if 'analysis_results' in st.session_state:
        results = st.session_state.analysis_results
        mask_str = results.get('mask')
        area = results.get('area')
        tissue_analysis = results.get('tissue_analysis')

        st.markdown("---")
        st.subheader("Predicted Wound Mask")
        mask_image = Image.open(io.BytesIO(base64.b64decode(mask_str)))
        st.image(mask_image, use_column_width=True)

        st.metric(label="Wound Area (% of image)", value=f"{area}%")

        if tissue_analysis:
            st.subheader("Tissue Analysis")
            st.bar_chart(tissue_analysis)

        # --- Fetch and display history ---
        st.markdown("---")
        st.subheader("Healing History")
        history_response = requests.get(BACKEND_URL_HISTORY, params={'patient_id': patient_id})
        if history_response.status_code == 200:
            history_data = history_response.json()
            st.session_state.history_data = history_data
            if history_data:
                df = pd.DataFrame(history_data)
                df['date'] = pd.to_datetime(df['timestamp'], unit='s')
                st.line_chart(df, x='date', y='area')
            else:
                st.write("No history found for this patient yet.")
        else:
            st.error("Could not fetch healing history.")
    else:
        st.info("Results will be displayed here after analysis.")
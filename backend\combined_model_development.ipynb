import os
import shutil
import kagglehub
import zipfile

print("Step 2: Preparing the combined dataset...")

# --- Configuration ---
# Path to the first dataset (from KaggleHub)
DATASET_1_ID = "leoscode/wound-segmentation-images"
# Path to the second dataset (from KaggleHub)
DATASET_2_ID = "ipunguhbpwt/fusc-data"

# Path to our new, combined dataset folder
COMBINED_DATA_PATH = r'D:\hackathon_project\backend\combined_dataset'

# --- Function to copy files ---
def copy_files(src_image_dir, src_mask_dir, dest_image_dir, dest_mask_dir):
    if not os.path.exists(src_image_dir) or not os.path.exists(src_mask_dir):
        print(f"Warning: Source directory not found: {src_image_dir} or {src_mask_dir}")
        return 0
    
    copied_count = 0
    image_files = os.listdir(src_image_dir)
    for filename in image_files:
        mask_path = os.path.join(src_mask_dir, filename)
        if os.path.exists(mask_path):
            shutil.copy(os.path.join(src_image_dir, filename), dest_image_dir)
            shutil.copy(mask_path, dest_mask_dir)
            copied_count += 1
    return copied_count

# --- Create directories for the new combined dataset ---
train_img_dest = os.path.join(COMBINED_DATA_PATH, 'train', 'images')
train_mask_dest = os.path.join(COMBINED_DATA_PATH, 'train', 'masks')
val_img_dest = os.path.join(COMBINED_DATA_PATH, 'val', 'images')
val_mask_dest = os.path.join(COMBINED_DATA_PATH, 'val', 'masks')

if os.path.exists(COMBINED_DATA_PATH):
    shutil.rmtree(COMBINED_DATA_PATH)

os.makedirs(train_img_dest, exist_ok=True)
os.makedirs(train_mask_dest, exist_ok=True)
os.makedirs(val_img_dest, exist_ok=True)
os.makedirs(val_mask_dest, exist_ok=True)
print("Created new directories for the combined dataset.")

# --- 1. Process the first Kaggle dataset ---
try:
    path_to_dataset1 = kagglehub.dataset_download(DATASET_1_ID)
    base_path1 = os.path.join(str(path_to_dataset1), 'data_wound_seg')
    
    num_train1 = copy_files(
        os.path.join(base_path1, 'train_images'),
        os.path.join(base_path1, 'train_masks'),
        train_img_dest, train_mask_dest
    )
    num_val1 = copy_files(
        os.path.join(base_path1, 'test_images'),
        os.path.join(base_path1, 'test_masks'),
        val_img_dest, val_mask_dest
    )
    print(f"Copied {num_train1} train and {num_val1} val files from {DATASET_1_ID}.")
except Exception as e:
    print(f"Could not process {DATASET_1_ID}: {e}")

# --- 2. Process the second Kaggle dataset (FUSC) ---
try:
    path_to_dataset2_zip = kagglehub.dataset_download(DATASET_2_ID)
    unzip_path = os.path.join(os.path.dirname(path_to_dataset2_zip), 'fusc_unzipped')
    with zipfile.ZipFile(path_to_dataset2_zip, 'r') as zip_ref:
        zip_ref.extractall(unzip_path)
    
    base_path2 = os.path.join(unzip_path, 'fusc-data')
    
    num_train2 = copy_files(
        os.path.join(base_path2, 'train_images'),
        os.path.join(base_path2, 'train_masks'),
        train_img_dest, train_mask_dest
    )
    num_val2 = copy_files(
        os.path.join(base_path2, 'val_images'),
        os.path.join(base_path2, 'val_masks'),
        val_img_dest, val_mask_dest
    )
    print(f"Copied {num_train2} train and {num_val2} val files from {DATASET_2_ID}.")
except Exception as e:
     print(f"Could not process {DATASET_2_ID}: {e}")

# --- Final Verification ---
total_train = len(os.listdir(train_img_dest))
total_val = len(os.listdir(val_img_dest))

print("\n--- SUCCESS! ---")
print("Combined dataset created successfully.")
print(f"Total training images: {total_train}")
print(f"Total validation images: {total_val}")
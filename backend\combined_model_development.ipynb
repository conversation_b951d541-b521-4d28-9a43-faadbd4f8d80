{"cells": [{"cell_type": "code", "execution_count": 4, "id": "e58951b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step 2: Preparing the combined dataset...\n", "Created new directories for the combined dataset.\n", "Copied 2208 train and 552 val files from leoscode/wound-segmentation-images.\n", "Could not process ipunguhbpwt/fusc-data: [Errno 13] Permission denied: 'C:\\\\Users\\\\<USER>\\\\.cache\\\\kagglehub\\\\datasets\\\\ipunguhbpwt\\\\fusc-data\\\\versions\\\\1'\n", "\n", "--- SUCCESS! ---\n", "Combined dataset created successfully.\n", "Total training images: 2208\n", "Total validation images: 552\n"]}], "source": ["import os\n", "import shutil\n", "import kagglehub\n", "import zipfile\n", "\n", "print(\"Step 2: Preparing the combined dataset...\")\n", "\n", "# --- Configuration ---\n", "# Path to the first dataset (from KaggleHub)\n", "DATASET_1_ID = \"leoscode/wound-segmentation-images\"\n", "# Path to the second dataset (from KaggleHub)\n", "DATASET_2_ID = \"ipunguhbpwt/fusc-data\"\n", "\n", "# Path to our new, combined dataset folder\n", "COMBINED_DATA_PATH = r'D:\\hackathon_project\\backend\\combined_dataset'\n", "\n", "# --- Function to copy files ---\n", "def copy_files(src_image_dir, src_mask_dir, dest_image_dir, dest_mask_dir):\n", "    if not os.path.exists(src_image_dir) or not os.path.exists(src_mask_dir):\n", "        print(f\"Warning: Source directory not found: {src_image_dir} or {src_mask_dir}\")\n", "        return 0\n", "    \n", "    copied_count = 0\n", "    image_files = os.listdir(src_image_dir)\n", "    for filename in image_files:\n", "        mask_path = os.path.join(src_mask_dir, filename)\n", "        if os.path.exists(mask_path):\n", "            shutil.copy(os.path.join(src_image_dir, filename), dest_image_dir)\n", "            shutil.copy(mask_path, dest_mask_dir)\n", "            copied_count += 1\n", "    return copied_count\n", "\n", "# --- Create directories for the new combined dataset ---\n", "train_img_dest = os.path.join(COMBINED_DATA_PATH, 'train', 'images')\n", "train_mask_dest = os.path.join(COMBINED_DATA_PATH, 'train', 'masks')\n", "val_img_dest = os.path.join(COMBINED_DATA_PATH, 'val', 'images')\n", "val_mask_dest = os.path.join(COMBINED_DATA_PATH, 'val', 'masks')\n", "\n", "if os.path.exists(COMBINED_DATA_PATH):\n", "    shutil.rmtree(COMBINED_DATA_PATH)\n", "\n", "os.makedirs(train_img_dest, exist_ok=True)\n", "os.makedirs(train_mask_dest, exist_ok=True)\n", "os.makedirs(val_img_dest, exist_ok=True)\n", "os.makedirs(val_mask_dest, exist_ok=True)\n", "print(\"Created new directories for the combined dataset.\")\n", "\n", "# --- 1. Process the first Kaggle dataset ---\n", "try:\n", "    path_to_dataset1 = kagglehub.dataset_download(DATASET_1_ID)\n", "    base_path1 = os.path.join(str(path_to_dataset1), 'data_wound_seg')\n", "    \n", "    num_train1 = copy_files(\n", "        os.path.join(base_path1, 'train_images'),\n", "        os.path.join(base_path1, 'train_masks'),\n", "        train_img_dest, train_mask_dest\n", "    )\n", "    num_val1 = copy_files(\n", "        os.path.join(base_path1, 'test_images'),\n", "        os.path.join(base_path1, 'test_masks'),\n", "        val_img_dest, val_mask_dest\n", "    )\n", "    print(f\"Copied {num_train1} train and {num_val1} val files from {DATASET_1_ID}.\")\n", "except Exception as e:\n", "    print(f\"Could not process {DATASET_1_ID}: {e}\")\n", "\n", "# --- 2. Process the second Kaggle dataset (FUSC) ---\n", "try:\n", "    path_to_dataset2_zip = kagglehub.dataset_download(DATASET_2_ID)\n", "    unzip_path = os.path.join(os.path.dirname(path_to_dataset2_zip), 'fusc_unzipped')\n", "    with zipfile.ZipFile(path_to_dataset2_zip, 'r') as zip_ref:\n", "        zip_ref.extractall(unzip_path)\n", "    \n", "    base_path2 = os.path.join(unzip_path, 'fusc-data')\n", "    \n", "    num_train2 = copy_files(\n", "        os.path.join(base_path2, 'train_images'),\n", "        os.path.join(base_path2, 'train_masks'),\n", "        train_img_dest, train_mask_dest\n", "    )\n", "    num_val2 = copy_files(\n", "        os.path.join(base_path2, 'val_images'),\n", "        os.path.join(base_path2, 'val_masks'),\n", "        val_img_dest, val_mask_dest\n", "    )\n", "    print(f\"Copied {num_train2} train and {num_val2} val files from {DATASET_2_ID}.\")\n", "except Exception as e:\n", "     print(f\"Could not process {DATASET_2_ID}: {e}\")\n", "\n", "# --- Final Verification ---\n", "total_train = len(os.listdir(train_img_dest))\n", "total_val = len(os.listdir(val_img_dest))\n", "\n", "print(\"\\n--- SUCCESS! ---\")\n", "print(\"Combined dataset created successfully.\")\n", "print(f\"Total training images: {total_train}\")\n", "print(f\"Total validation images: {total_val}\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}
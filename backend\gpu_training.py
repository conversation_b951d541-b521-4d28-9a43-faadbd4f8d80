#!/usr/bin/env python3
"""
GPU-Optimized Wound Segmentation Model Training Pipeline
High-performance training with GPU acceleration, mixed precision, and optimized batch processing.
"""

import os
import json
import numpy as np
import tensorflow as tf
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime

# Configure GPU settings for optimal performance
def configure_gpu():
    """Configure GPU settings for optimal training performance."""
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            # Enable memory growth to avoid allocating all GPU memory at once
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            
            # Set mixed precision for faster training
            tf.keras.mixed_precision.set_global_policy('mixed_float16')
            
            print(f"✅ GPU Configuration Complete!")
            print(f"   - Found {len(gpus)} GPU(s): {[gpu.name for gpu in gpus]}")
            print(f"   - Memory growth enabled")
            print(f"   - Mixed precision (float16) enabled for faster training")
            return True
        except RuntimeError as e:
            print(f"❌ GPU configuration error: {e}")
            return False
    else:
        print("⚠️  No GPU detected. Training will use CPU (slower).")
        print("   For faster training, consider using a GPU-enabled environment.")
        return False

# Configure GPU at module import
GPU_AVAILABLE = configure_gpu()

# Set random seeds for reproducibility
tf.random.set_seed(42)
np.random.seed(42)

class GPUOptimizedWoundTrainer:
    def __init__(self, config):
        self.config = config
        self.model = None
        self.history = None
        
        # Optimize batch size based on GPU availability
        if GPU_AVAILABLE:
            # Increase batch size for GPU training
            self.config['batch_size'] = min(32, self.config.get('batch_size', 16) * 2)
            print(f"🚀 GPU detected: Optimized batch size to {self.config['batch_size']}")
        else:
            # Optimize for CPU training - use smaller batch size but more efficient processing
            self.config['batch_size'] = max(4, self.config.get('batch_size', 16) // 4)
            print(f"💻 CPU mode: Optimized batch size to {self.config['batch_size']} for CPU efficiency")
            print("   💡 Tip: For GPU acceleration, install CUDA-enabled TensorFlow or use Google Colab")
        
        # Create output directories
        self.output_dir = Path(config['output_dir'])
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / 'models').mkdir(exist_ok=True)
        (self.output_dir / 'plots').mkdir(exist_ok=True)
        (self.output_dir / 'logs').mkdir(exist_ok=True)
        
    def load_dataset_paths(self):
        """Load and prepare dataset file paths."""
        print("Loading dataset paths...")
        
        dataset_path = Path(self.config['dataset_path'])
        
        # Training data
        train_images_dir = dataset_path / 'train' / 'images'
        train_masks_dir = dataset_path / 'train' / 'masks'
        
        # Validation data
        val_images_dir = dataset_path / 'val' / 'images'
        val_masks_dir = dataset_path / 'val' / 'masks'
        
        # Get file paths
        train_image_paths = sorted([str(p) for p in train_images_dir.iterdir() if p.is_file()])
        train_mask_paths = sorted([str(p) for p in train_masks_dir.iterdir() if p.is_file()])
        
        val_image_paths = sorted([str(p) for p in val_images_dir.iterdir() if p.is_file()])
        val_mask_paths = sorted([str(p) for p in val_masks_dir.iterdir() if p.is_file()])
        
        print(f"Training samples: {len(train_image_paths)}")
        print(f"Validation samples: {len(val_image_paths)}")
        
        return train_image_paths, train_mask_paths, val_image_paths, val_mask_paths
    
    def preprocess_image(self, image_path, mask_path):
        """Preprocess image and mask with GPU-optimized operations."""
        # Load image
        image = tf.io.read_file(image_path)
        image = tf.image.decode_image(image, channels=3, expand_animations=False)
        image = tf.cast(image, tf.float32)
        
        # Load mask
        mask = tf.io.read_file(mask_path)
        mask = tf.image.decode_image(mask, channels=1, expand_animations=False)
        mask = tf.cast(mask, tf.float32)
        
        # Resize to target size
        image = tf.image.resize(image, [self.config['img_height'], self.config['img_width']])
        mask = tf.image.resize(mask, [self.config['img_height'], self.config['img_width']])
        
        # Normalize image to [0, 1]
        image = image / 255.0
        
        # Normalize mask to binary [0, 1]
        mask = tf.cast(mask > 127.5, tf.float32)
        
        return image, mask
    
    def augment_data(self, image, mask):
        """GPU-optimized data augmentation."""
        # Random horizontal flip
        if tf.random.uniform(()) > 0.5:
            image = tf.image.flip_left_right(image)
            mask = tf.image.flip_left_right(mask)
        
        # Random vertical flip
        if tf.random.uniform(()) > 0.5:
            image = tf.image.flip_up_down(image)
            mask = tf.image.flip_up_down(mask)
        
        # Random brightness and contrast (only for image)
        if tf.random.uniform(()) > 0.5:
            image = tf.image.random_brightness(image, 0.1)
            image = tf.image.random_contrast(image, 0.8, 1.2)
            image = tf.clip_by_value(image, 0.0, 1.0)
        
        return image, mask
    
    def create_dataset(self, image_paths, mask_paths, is_training=True):
        """Create optimized TensorFlow dataset."""
        print(f"Creating {'training' if is_training else 'validation'} dataset...")
        
        # Create dataset from paths
        dataset = tf.data.Dataset.from_tensor_slices((image_paths, mask_paths))
        
        # Map preprocessing function
        dataset = dataset.map(
            self.preprocess_image, 
            num_parallel_calls=tf.data.AUTOTUNE
        )
        
        if is_training:
            # Apply augmentation for training data
            dataset = dataset.map(
                self.augment_data,
                num_parallel_calls=tf.data.AUTOTUNE
            )
            # Shuffle training data
            dataset = dataset.shuffle(buffer_size=1000)
        
        # Batch and prefetch for optimal performance
        dataset = dataset.batch(self.config['batch_size'])
        dataset = dataset.prefetch(buffer_size=tf.data.AUTOTUNE)
        
        return dataset
    
    def build_optimized_unet(self):
        """Build GPU-optimized U-Net model with mixed precision support."""
        print("Building optimized U-Net model...")
        
        inputs = tf.keras.layers.Input(
            shape=(self.config['img_height'], self.config['img_width'], 3),
            name='input_layer'
        )
        
        # Encoder (Contracting Path)
        # Block 1
        c1 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)
        c1 = tf.keras.layers.BatchNormalization()(c1)
        c1 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(c1)
        c1 = tf.keras.layers.BatchNormalization()(c1)
        p1 = tf.keras.layers.MaxPooling2D((2, 2))(c1)
        p1 = tf.keras.layers.Dropout(0.1)(p1)
        
        # Block 2
        c2 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(p1)
        c2 = tf.keras.layers.BatchNormalization()(c2)
        c2 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(c2)
        c2 = tf.keras.layers.BatchNormalization()(c2)
        p2 = tf.keras.layers.MaxPooling2D((2, 2))(c2)
        p2 = tf.keras.layers.Dropout(0.1)(p2)
        
        # Block 3
        c3 = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')(p2)
        c3 = tf.keras.layers.BatchNormalization()(c3)
        c3 = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')(c3)
        c3 = tf.keras.layers.BatchNormalization()(c3)
        p3 = tf.keras.layers.MaxPooling2D((2, 2))(c3)
        p3 = tf.keras.layers.Dropout(0.2)(p3)
        
        # Block 4
        c4 = tf.keras.layers.Conv2D(256, (3, 3), activation='relu', padding='same')(p3)
        c4 = tf.keras.layers.BatchNormalization()(c4)
        c4 = tf.keras.layers.Conv2D(256, (3, 3), activation='relu', padding='same')(c4)
        c4 = tf.keras.layers.BatchNormalization()(c4)
        p4 = tf.keras.layers.MaxPooling2D((2, 2))(c4)
        p4 = tf.keras.layers.Dropout(0.2)(p4)
        
        # Bottleneck
        c5 = tf.keras.layers.Conv2D(512, (3, 3), activation='relu', padding='same')(p4)
        c5 = tf.keras.layers.BatchNormalization()(c5)
        c5 = tf.keras.layers.Conv2D(512, (3, 3), activation='relu', padding='same')(c5)
        c5 = tf.keras.layers.BatchNormalization()(c5)
        c5 = tf.keras.layers.Dropout(0.3)(c5)
        
        # Decoder (Expansive Path)
        # Block 6
        u6 = tf.keras.layers.Conv2DTranspose(256, (2, 2), strides=(2, 2), padding='same')(c5)
        u6 = tf.keras.layers.concatenate([u6, c4])
        c6 = tf.keras.layers.Conv2D(256, (3, 3), activation='relu', padding='same')(u6)
        c6 = tf.keras.layers.BatchNormalization()(c6)
        c6 = tf.keras.layers.Conv2D(256, (3, 3), activation='relu', padding='same')(c6)
        c6 = tf.keras.layers.BatchNormalization()(c6)
        c6 = tf.keras.layers.Dropout(0.2)(c6)
        
        # Block 7
        u7 = tf.keras.layers.Conv2DTranspose(128, (2, 2), strides=(2, 2), padding='same')(c6)
        u7 = tf.keras.layers.concatenate([u7, c3])
        c7 = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')(u7)
        c7 = tf.keras.layers.BatchNormalization()(c7)
        c7 = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')(c7)
        c7 = tf.keras.layers.BatchNormalization()(c7)
        c7 = tf.keras.layers.Dropout(0.2)(c7)
        
        # Block 8
        u8 = tf.keras.layers.Conv2DTranspose(64, (2, 2), strides=(2, 2), padding='same')(c7)
        u8 = tf.keras.layers.concatenate([u8, c2])
        c8 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(u8)
        c8 = tf.keras.layers.BatchNormalization()(c8)
        c8 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(c8)
        c8 = tf.keras.layers.BatchNormalization()(c8)
        c8 = tf.keras.layers.Dropout(0.1)(c8)
        
        # Block 9
        u9 = tf.keras.layers.Conv2DTranspose(32, (2, 2), strides=(2, 2), padding='same')(c8)
        u9 = tf.keras.layers.concatenate([u9, c1])
        c9 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(u9)
        c9 = tf.keras.layers.BatchNormalization()(c9)
        c9 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(c9)
        c9 = tf.keras.layers.BatchNormalization()(c9)
        c9 = tf.keras.layers.Dropout(0.1)(c9)
        
        # Output layer
        outputs = tf.keras.layers.Conv2D(1, (1, 1), activation='sigmoid', dtype='float32')(c9)
        
        model = tf.keras.models.Model(inputs=[inputs], outputs=[outputs])
        
        return model
    
    def compile_model(self):
        """Compile model with optimized settings."""
        print("Compiling model...")
        
        # Custom metrics
        def dice_coefficient(y_true, y_pred):
            smooth = 1e-6
            y_true_f = tf.keras.backend.flatten(y_true)
            y_pred_f = tf.keras.backend.flatten(y_pred)
            intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
            return (2. * intersection + smooth) / (tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) + smooth)
        
        def iou_metric(y_true, y_pred):
            smooth = 1e-6
            y_true_f = tf.keras.backend.flatten(y_true)
            y_pred_f = tf.keras.backend.flatten(y_pred)
            intersection = tf.keras.backend.sum(y_true_f * y_pred_f)
            union = tf.keras.backend.sum(y_true_f) + tf.keras.backend.sum(y_pred_f) - intersection
            return (intersection + smooth) / (union + smooth)
        
        # Combined loss function
        def combined_loss(y_true, y_pred):
            bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
            dice_loss = 1 - dice_coefficient(y_true, y_pred)
            return 0.5 * bce + 0.5 * dice_loss
        
        # Compile with optimized optimizer
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=self.config['learning_rate'],
            clipnorm=1.0  # Gradient clipping for stability
        )
        
        self.model.compile(
            optimizer=optimizer,
            loss=combined_loss,
            metrics=['accuracy', dice_coefficient, iou_metric]
        )
        
        print("Model compiled successfully!")
        print(f"Total parameters: {self.model.count_params():,}")
        
    def get_callbacks(self):
        """Get optimized callbacks for training."""
        callbacks_list = []
        
        # Model checkpoint
        checkpoint_callback = tf.keras.callbacks.ModelCheckpoint(
            filepath=str(self.output_dir / 'models' / 'best_model.keras'),
            monitor='val_dice_coefficient',
            mode='max',
            save_best_only=True,
            save_weights_only=False,
            verbose=1
        )
        callbacks_list.append(checkpoint_callback)
        
        # Early stopping
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_dice_coefficient',
            mode='max',
            patience=self.config['early_stopping_patience'],
            restore_best_weights=True,
            verbose=1
        )
        callbacks_list.append(early_stopping)
        
        # Reduce learning rate on plateau
        reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        )
        callbacks_list.append(reduce_lr)
        
        # CSV logger
        csv_logger = tf.keras.callbacks.CSVLogger(
            str(self.output_dir / 'logs' / 'training_log.csv'),
            append=True
        )
        callbacks_list.append(csv_logger)
        
        return callbacks_list
    
    def train(self):
        """Execute the complete training pipeline."""
        print("=" * 60)
        print("🚀 GPU-OPTIMIZED WOUND SEGMENTATION TRAINING PIPELINE")
        print("=" * 60)
        
        # Load dataset
        train_image_paths, train_mask_paths, val_image_paths, val_mask_paths = self.load_dataset_paths()
        
        # Create datasets
        train_dataset = self.create_dataset(train_image_paths, train_mask_paths, is_training=True)
        val_dataset = self.create_dataset(val_image_paths, val_mask_paths, is_training=False)
        
        # Build and compile model
        self.model = self.build_optimized_unet()
        self.compile_model()
        
        # Get callbacks
        callbacks = self.get_callbacks()
        
        # Start training
        print(f"\n🎯 Starting training for {self.config['epochs']} epochs...")
        print(f"   - Batch size: {self.config['batch_size']}")
        print(f"   - Learning rate: {self.config['learning_rate']}")
        print(f"   - GPU enabled: {GPU_AVAILABLE}")
        
        start_time = datetime.now()
        
        self.history = self.model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=self.config['epochs'],
            callbacks=callbacks,
            verbose=1
        )
        
        end_time = datetime.now()
        training_time = end_time - start_time
        
        print(f"\n✅ Training completed!")
        print(f"   - Total time: {training_time}")
        print(f"   - Average time per epoch: {training_time / self.config['epochs']}")
        
        return self.history


def main():
    """Main training function."""
    # Configuration
    config = {
        'dataset_path': 'combined_dataset',
        'output_dir': 'training_output',
        'img_height': 256,
        'img_width': 256,
        'batch_size': 16,  # Will be optimized based on GPU availability
        'epochs': 10,
        'learning_rate': 0.0001,
        'early_stopping_patience': 10
    }
    
    # Save configuration
    with open('training_output/gpu_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("GPU-Optimized Configuration saved!")
    print(json.dumps(config, indent=2))
    
    # Initialize trainer and start training
    trainer = GPUOptimizedWoundTrainer(config)
    history = trainer.train()
    
    print("\n🎉 GPU-optimized training pipeline completed successfully!")


if __name__ == "__main__":
    main()

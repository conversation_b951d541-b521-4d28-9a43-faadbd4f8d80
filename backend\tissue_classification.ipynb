{"cells": [{"cell_type": "code", "execution_count": 3, "id": "db4a56db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting mat<PERSON><PERSON><PERSON><PERSON>\n", "  Using cached matplotlib-3.10.6-cp313-cp313-win_amd64.whl.metadata (11 kB)\n", "Collecting contourpy>=1.0.1 (from matplotlib)\n", "  Using cached contourpy-1.3.3-cp313-cp313-win_amd64.whl.metadata (5.5 kB)\n", "Collecting cycler>=0.10 (from matplotlib)\n", "  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting fonttools>=4.22.0 (from matplotlib)\n", "  Using cached fonttools-4.60.0-cp313-cp313-win_amd64.whl.metadata (113 kB)\n", "Collecting kiwisolver>=1.3.1 (from matplotlib)\n", "  Using cached kiwisolver-1.4.9-cp313-cp313-win_amd64.whl.metadata (6.4 kB)\n", "Requirement already satisfied: numpy>=1.23 in .\\venv\\lib\\site-packages (from matplotlib) (2.3.3)\n", "Requirement already satisfied: packaging>=20.0 in .\\venv\\lib\\site-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pillow>=8 in .\\venv\\lib\\site-packages (from matplotlib) (11.3.0)\n", "Collecting pyparsing>=2.3.1 (from matplotlib)\n", "  Using cached pyparsing-3.2.5-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: python-dateutil>=2.7 in .\\venv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in .\\venv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Using cached matplotlib-3.10.6-cp313-cp313-win_amd64.whl (8.1 MB)\n", "Using cached contourpy-1.3.3-cp313-cp313-win_amd64.whl (226 kB)\n", "Using cached cycler-0.12.1-py3-none-any.whl (8.3 kB)\n", "Using cached fonttools-4.60.0-cp313-cp313-win_amd64.whl (2.3 MB)\n", "Using cached kiwisolver-1.4.9-cp313-cp313-win_amd64.whl (73 kB)\n", "Using cached pyparsing-3.2.5-py3-none-any.whl (113 kB)\n", "Installing collected packages: pyparsing, kiwisolver, fonttools, cycler, contourpy, matplotlib\n", "Successfully installed contourpy-1.3.3 cycler-0.12.1 fonttools-4.60.0 kiwisolver-1.4.9 matplotlib-3.10.6 pyparsing-3.2.5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": 12, "id": "4c92e4c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setting up for tissue classification...\n", "Loading training and validation datasets...\n", "Found 660 files belonging to 2 classes.\n", "Using 528 files for training.\n", "Found 660 files belonging to 2 classes.\n", "Using 132 files for validation.\n", "Found Classes: ['Original', 'Padded']\n", "Downloading data from https://storage.googleapis.com/tensorflow/keras-applications/mobilenet_v2/mobilenet_v2_weights_tf_dim_ordering_tf_kernels_1.0_224_no_top.h5\n", "\u001b[1m9406464/9406464\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m40s\u001b[0m 4us/step\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"functional_1\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"functional_1\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ input_layer_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ sequential_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Sequential</span>)       │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ true_divide (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">TrueDivide</span>)        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ subtract (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Subtract</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ mobilenetv2_1.00_224            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)     │     <span style=\"color: #00af00; text-decoration-color: #00af00\">2,257,984</span> │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Functional</span>)                    │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ global_average_pooling2d        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GlobalAveragePooling2D</span>)        │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)            │       <span style=\"color: #00af00; text-decoration-color: #00af00\">163,968</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">2</span>)              │           <span style=\"color: #00af00; text-decoration-color: #00af00\">258</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ input_layer_2 (\u001b[38;5;33mInput<PERSON>ayer\u001b[0m)      │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ sequential_1 (\u001b[38;5;33mSequential\u001b[0m)       │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ true_divide (\u001b[38;5;33mTrueDivide\u001b[0m)        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ subtract (\u001b[38;5;33mSubtract\u001b[0m)             │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ mobilenetv2_1.00_224            │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m1280\u001b[0m)     │     \u001b[38;5;34m2,257,984\u001b[0m │\n", "│ (\u001b[38;5;33mFunctional\u001b[0m)                    │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ global_average_pooling2d        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1280\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "│ (\u001b[38;5;33mGlobalAveragePooling2D\u001b[0m)        │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m128\u001b[0m)            │       \u001b[38;5;34m163,968\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m2\u001b[0m)              │           \u001b[38;5;34m258\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,422,210</span> (9.24 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m2,422,210\u001b[0m (9.24 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">164,226</span> (641.51 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m164,226\u001b[0m (641.51 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,257,984</span> (8.61 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m2,257,984\u001b[0m (8.61 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Starting tissue classifier training...\n", "Epoch 1/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m9s\u001b[0m 383ms/step - accuracy: 0.7140 - loss: 0.5179 - val_accuracy: 0.7803 - val_loss: 0.4293\n", "Epoch 2/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 308ms/step - accuracy: 0.8693 - loss: 0.3152 - val_accuracy: 0.8409 - val_loss: 0.3366\n", "Epoch 3/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 306ms/step - accuracy: 0.8769 - loss: 0.2902 - val_accuracy: 0.8409 - val_loss: 0.2942\n", "Epoch 4/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 315ms/step - accuracy: 0.8750 - loss: 0.2741 - val_accuracy: 0.7879 - val_loss: 0.3974\n", "Epoch 5/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 317ms/step - accuracy: 0.8769 - loss: 0.2477 - val_accuracy: 0.9015 - val_loss: 0.2740\n", "Epoch 6/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 317ms/step - accuracy: 0.8864 - loss: 0.2529 - val_accuracy: 0.8712 - val_loss: 0.2856\n", "Epoch 7/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 307ms/step - accuracy: 0.8807 - loss: 0.2510 - val_accuracy: 0.8864 - val_loss: 0.2840\n", "Epoch 8/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 316ms/step - accuracy: 0.9015 - loss: 0.2192 - val_accuracy: 0.8712 - val_loss: 0.2997\n", "Epoch 9/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 317ms/step - accuracy: 0.8977 - loss: 0.2306 - val_accuracy: 0.8788 - val_loss: 0.2870\n", "Epoch 10/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 308ms/step - accuracy: 0.8826 - loss: 0.2403 - val_accuracy: 0.9015 - val_loss: 0.2511\n", "Epoch 11/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 329ms/step - accuracy: 0.8883 - loss: 0.2275 - val_accuracy: 0.8939 - val_loss: 0.3137\n", "Epoch 12/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 316ms/step - accuracy: 0.8920 - loss: 0.2187 - val_accuracy: 0.8712 - val_loss: 0.3345\n", "Epoch 13/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 311ms/step - accuracy: 0.9129 - loss: 0.2235 - val_accuracy: 0.8788 - val_loss: 0.3294\n", "Epoch 14/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 299ms/step - accuracy: 0.9167 - loss: 0.2007 - val_accuracy: 0.8409 - val_loss: 0.4085\n", "Epoch 15/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 298ms/step - accuracy: 0.9015 - loss: 0.2131 - val_accuracy: 0.8712 - val_loss: 0.3766\n", "Epoch 16/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 299ms/step - accuracy: 0.8788 - loss: 0.2488 - val_accuracy: 0.8939 - val_loss: 0.3024\n", "Epoch 17/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 310ms/step - accuracy: 0.8977 - loss: 0.2269 - val_accuracy: 0.8712 - val_loss: 0.4033\n", "Epoch 18/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 300ms/step - accuracy: 0.9129 - loss: 0.2000 - val_accuracy: 0.8712 - val_loss: 0.3434\n", "Epoch 19/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 301ms/step - accuracy: 0.9034 - loss: 0.2018 - val_accuracy: 0.8864 - val_loss: 0.3360\n", "Epoch 20/20\n", "\u001b[1m17/17\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 298ms/step - accuracy: 0.9110 - loss: 0.1990 - val_accuracy: 0.8106 - val_loss: 0.4766\n", "Classifier training complete!\n", "Tissue classifier model saved successfully!\n"]}, {"data": {"image/png": "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*******************************************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", "text/plain": ["<Figure size 800x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import tensorflow as tf\n", "from tensorflow.keras import layers, models\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "print(\"Setting up for tissue classification...\")\n", "\n", "# --- 1. CONFIGURATION ---\n", "TISSUE_DATASET_PATH = r'D:\\EXTRAIDEAS\\DFUTissueSegNet-main\\DFUTissueSegNet-main\\DFUTissue\\Labeled' \n", "\n", "IMG_HEIGHT = 224\n", "IMG_WIDTH = 224\n", "BATCH_SIZE = 32\n", "EPOCHS = 20\n", "\n", "# --- 2. LOAD THE DATASET ---\n", "print(\"Loading training and validation datasets...\")\n", "try:\n", "    train_dataset = tf.keras.utils.image_dataset_from_directory(\n", "        TISSUE_DATASET_PATH,\n", "        validation_split=0.2,\n", "        subset=\"training\",\n", "        seed=123,\n", "        image_size=(IMG_HEIGHT, IMG_WIDTH),\n", "        batch_size=BATCH_SIZE\n", "    )\n", "\n", "    validation_dataset = tf.keras.utils.image_dataset_from_directory(\n", "        TISSUE_DATASET_PATH,\n", "        validation_split=0.2,\n", "        subset=\"validation\",\n", "        seed=123,\n", "        image_size=(IMG_HEIGHT, IMG_WIDTH),\n", "        batch_size=BATCH_SIZE\n", "    )\n", "\n", "    class_names = train_dataset.class_names\n", "    print(\"Found Classes:\", class_names)\n", "\n", "    # --- 3. BUILD THE CLASSIFICATION MODEL WITH AUGMENTATION ---\n", "    data_augmentation = tf.keras.Sequential([\n", "        layers.RandomFlip(\"horizontal_and_vertical\"),\n", "        layers.RandomRotation(0.2),\n", "        layers.RandomZoom(0.2),\n", "    ])\n", "\n", "    base_model = tf.keras.applications.MobileNetV2(input_shape=(IMG_HEIGHT, IMG_WIDTH, 3),\n", "                                                   include_top=False,\n", "                                                   weights='imagenet')\n", "    # THIS LINE IS NOW FIXED\n", "    base_model.trainable = False \n", "\n", "    inputs = tf.keras.Input(shape=(IMG_HEIGHT, IMG_WIDTH, 3))\n", "    x = data_augmentation(inputs)\n", "    x = tf.keras.applications.mobilenet_v2.preprocess_input(x)\n", "    x = base_model(x, training=False)\n", "    x = layers.GlobalAveragePooling2D()(x) \n", "    x = layers.Dense(128, activation='relu')(x)\n", "    outputs = layers.Dense(len(class_names), activation='softmax')(x)\n", "    model_classifier = tf.keras.Model(inputs, outputs)\n", "\n", "    model_classifier.compile(optimizer='adam',\n", "                  loss='sparse_categorical_crossentropy',\n", "                  metrics=['accuracy'])\n", "\n", "    model_classifier.summary()\n", "\n", "    # --- 4. T<PERSON><PERSON> THE MODEL ---\n", "    print(\"\\nStarting tissue classifier training...\")\n", "    history_classifier = model_classifier.fit(\n", "        train_dataset,\n", "        validation_data=validation_dataset,\n", "        epochs=EPOCHS\n", "    )\n", "    print(\"Classifier training complete!\")\n", "\n", "    # --- 5. SAVE THE MODEL ---\n", "    model_classifier.save('tissue_classifier_model_v1.keras')\n", "    print(\"Tissue classifier model saved successfully!\")\n", "\n", "    # --- 6. PLOT RESULTS ---\n", "    acc = history_classifier.history['accuracy']\n", "    val_acc = history_classifier.history['val_accuracy']\n", "    loss = history_classifier.history['loss']\n", "    val_loss = history_classifier.history['val_loss']\n", "\n", "    plt.figure(figsize=(8, 8))\n", "    plt.subplot(2, 1, 1)\n", "    plt.plot(acc, label='Training Accuracy')\n", "    plt.plot(val_acc, label='Validation Accuracy')\n", "    plt.legend(loc='lower right')\n", "    plt.title('Training and Validation Accuracy')\n", "\n", "    plt.subplot(2, 1, 2)\n", "    plt.plot(loss, label='Training Loss')\n", "    plt.plot(val_loss, label='Validation Loss')\n", "    plt.legend(loc='upper right')\n", "    plt.title('Training and Validation Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.show()\n", "\n", "except FileNotFoundError:\n", "    print(f\"ERROR: The directory was not found at '{TISSUE_DATASET_PATH}'\")\n", "    print(\"Please make sure this is the correct path to the 'Labeled' folder.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}